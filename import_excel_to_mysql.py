#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据导入MySQL脚本
用于将销售出库明细账Excel文件导入到MySQL数据库
"""

import pandas as pd
import pymysql
from sqlalchemy import create_engine
import numpy as np
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ExcelToMySQLImporter:
    def __init__(self, mysql_config):
        """
        初始化导入器
        
        Args:
            mysql_config (dict): MySQL连接配置
                {
                    'host': 'localhost',
                    'port': 3306,
                    'user': 'your_username',
                    'password': 'your_password',
                    'database': 'sales_management',
                    'charset': 'utf8mb4'
                }
        """
        self.mysql_config = mysql_config
        self.engine = None
        
    def connect_mysql(self):
        """连接MySQL数据库"""
        try:
            connection_string = (
                f"mysql+pymysql://{self.mysql_config['user']}:"
                f"{self.mysql_config['password']}@{self.mysql_config['host']}:"
                f"{self.mysql_config['port']}/{self.mysql_config['database']}"
                f"?charset={self.mysql_config['charset']}"
            )
            self.engine = create_engine(connection_string, echo=False)
            logger.info("MySQL数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"MySQL数据库连接失败: {e}")
            return False
    
    def read_excel_file(self, excel_path):
        """读取Excel文件"""
        try:
            logger.info(f"正在读取Excel文件: {excel_path}")
            df = pd.read_excel(excel_path)
            logger.info(f"Excel文件读取成功，共{len(df)}行，{len(df.columns)}列")
            return df
        except Exception as e:
            logger.error(f"读取Excel文件失败: {e}")
            return None
    
    def clean_data(self, df):
        """清理数据"""
        logger.info("开始清理数据...")
        
        # 处理NaN值
        df = df.replace({np.nan: None})
        
        # 处理时间字段
        time_columns = ['付款时间', '发货时间', '下单时间', '审核时间']
        for col in time_columns:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')
        
        # 处理数值字段
        numeric_columns = [
            '货品数量', '货品原单价', '货品原总金额', '订单总优惠', '邮费',
            '货品成交价', '货品成交总价', '货品总优惠', '货到付款金额',
            '货品成本', '货品总成本', '固定成本', '固定总成本',
            '订单支付金额', '应收金额', '退款前支付金额', '单品支付金额',
            '分摊邮费', '预估邮资', '邮资成本', '订单包装成本',
            '订单毛利', '毛利率', '订单固定毛利', '固定毛利率',
            '实际重量', '预估重量', '来源组合装数量', '体积'
        ]
        
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 处理字符串字段长度
        string_columns = {
            '订单编号': 50, '原始单号': 50, '原始子订单号': 50, '订单类型': 20,
            '支付账号': 50, '出库单编号': 50, '仓库': 50, '仓库类型': 20,
            '店铺': 100, '出库单状态': 20, '出库状态': 50, '货品编号': 50,
            '货品名称': 200, '货品简称': 100, '品牌': 50, '分类': 100,
            '规格码': 50, '规格名称': 100, '平台规格名称': 200, '条形码': 50,
            '客户网名': 100, '收件人': 50, '证件号码': 30, '收货地区': 100,
            '收件人手机': 20, '收件人电话': 20, '物流公司': 100, '需开发票': 10,
            '制单人': 50, '打单员': 50, '拣货员': 50, '打包员': 50,
            '检视员': 50, '业务员': 50, '验货员': 50, '打印波次': 50,
            '物流单打印状态': 20, '发货单打印状态': 20, '分拣单打印状态': 20,
            '物流单号': 50, '分拣单编号': 50, '外部单号': 50, '赠品方式': 50,
            '包装': 100, '来源组合装编码': 50, '拆自组合装': 100,
            '分销商': 100, '分销商编号': 50, '分销原始单号': 50
        }
        
        for col, max_length in string_columns.items():
            if col in df.columns:
                df[col] = df[col].astype(str).str[:max_length]
                df[col] = df[col].replace('nan', None)
        
        logger.info("数据清理完成")
        return df
    
    def import_to_mysql(self, df, table_name='销售出库明细账', batch_size=1000):
        """导入数据到MySQL"""
        try:
            logger.info(f"开始导入数据到表: {table_name}")
            
            # 分批导入
            total_rows = len(df)
            for i in range(0, total_rows, batch_size):
                batch_df = df.iloc[i:i+batch_size]
                batch_df.to_sql(
                    name=table_name,
                    con=self.engine,
                    if_exists='append',
                    index=False,
                    method='multi'
                )
                logger.info(f"已导入 {min(i+batch_size, total_rows)}/{total_rows} 行")
            
            logger.info("数据导入完成")
            return True
            
        except Exception as e:
            logger.error(f"数据导入失败: {e}")
            return False
    
    def run_import(self, excel_path, table_name='销售出库明细账'):
        """执行完整的导入流程"""
        # 连接数据库
        if not self.connect_mysql():
            return False
        
        # 读取Excel文件
        df = self.read_excel_file(excel_path)
        if df is None:
            return False
        
        # 清理数据
        df = self.clean_data(df)
        
        # 导入数据
        return self.import_to_mysql(df, table_name)

def main():
    """主函数"""
    # MySQL配置 - 请根据实际情况修改
    mysql_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',  # 请修改为您的MySQL用户名
        'password': 'your_password',  # 请修改为您的MySQL密码
        'database': 'sales_management',
        'charset': 'utf8mb4'
    }
    
    # Excel文件路径
    excel_path = 'data/20250106销售出库明细账.xlsx'
    
    # 创建导入器并执行导入
    importer = ExcelToMySQLImporter(mysql_config)
    
    if importer.run_import(excel_path):
        print("数据导入成功！")
    else:
        print("数据导入失败！")

if __name__ == "__main__":
    main()
